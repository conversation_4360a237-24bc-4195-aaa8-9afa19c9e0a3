// Company Information
export const OWNER_NAME = '<PERSON>';
export const COMPANY_NAME = 'James <PERSON>ield Electrical Services Ltd.';
export const COMPANY_DESCRIPTION =
	'Domestic, agricultural, commercial & industrial electrical services across the Scottish Borders.';
export const COMPANY_LOCATION = 'Stichill, Scottish Borders';

// Contact Information
export const EMAIL = '<EMAIL>';
export const PHONE_NUMBER = '************';

// Social Media Information
export const FACEBOOK_URL = 'http://facebook.com/JDESborders';
export const GOOGLE_BUSINESS_URL = 'https://share.google/wxarUSuPsU2u5tZgE';

// SEO Information
export const SEO_TITLE = 'James Duffield Electrical Services Ltd.';
export const SEO_DESCRIPTION =
	'Domestic, agricultural, commercial & industrial electrical services across the Scottish Borders. Local, family business. Kelso, Greenlaw, Galashiels, Newton St Boswells, Duns, Coldstream and surrounding areas.';

export const JOB_STATUS_CONFIG = {
	launch: {
		message: 'Our official launch will be on 29th September 2025 - stay tuned!',
		bgColor: 'bg-green-700',
		textColor: 'text-white',
		icon: 'rocket'
	},
	open: {
		message: 'We are taking on new customers, contact us now to chat about your project',
		bgColor: 'bg-secondary',
		textColor: 'text-base-100',
		icon: 'rocket'
	},
	limited: {
		message:
			'To protect our current projects, we are currently limiting take-up of new jobs to December 2025 onwards',
		bgColor: 'bg-yellow-400',
		textColor: 'text-warning-content',
		icon: 'clock'
	},
	closed: {
		message: 'To protect our current projects, we are not accepting any new jobs at this time',
		bgColor: 'bg-red-700',
		textColor: 'text-base-100',
		icon: 'magnifying-glass'
	}
};

// Job Status Configuration
export const JOB_STATUS_TITLE = 'Update on 24th August 2025';

// Current Job Status - Change this value to control the banner
// Valid values: 'launch', 'open', 'limited', 'closed', 'hide'
export const CURRENT_JOB_STATUS = 'launch';

// Debug Configuration
export const DEBUG_MODE = false; // Set to true to enable all debug logging
